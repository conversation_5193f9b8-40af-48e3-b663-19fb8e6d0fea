﻿  dllmain.cpp
C:\Userfile\ccode\black_run_shellcode\hijack_tester\dllmain.cpp(25,1): warning C4005: “STATUS_TIMEOUT”: 宏重定义
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnt.h(2552,1):
  参见“STATUS_TIMEOUT”的前一个定义
  
C:\Userfile\ccode\black_run_shellcode\hijack_tester\dllmain.cpp(26,1): warning C4005: “STATUS_PENDING”: 宏重定义
  C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnt.h(2553,1):
  参见“STATUS_PENDING”的前一个定义
  
C:\Userfile\ccode\black_run_shellcode\hijack_tester\dllmain.cpp(618,33): warning C4244: “参数”: 从“DWORD_PTR”转换到“DWORD”，可能丢失数据
C:\Userfile\ccode\black_run_shellcode\hijack_tester\dllmain.cpp(701,29): warning C4244: “参数”: 从“DWORD_PTR”转换到“DWORD”，可能丢失数据
C:\Userfile\ccode\black_run_shellcode\hijack_tester\dllmain.cpp(1191,42): warning C4101: “e”: 未引用的局部变量
  hijack_tester.vcxproj -> C:\Userfile\ccode\black_run_shellcode\x64\Debug\hijack_tester.dll
